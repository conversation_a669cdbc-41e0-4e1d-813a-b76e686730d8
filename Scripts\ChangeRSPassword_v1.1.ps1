<#
.SYNOPSIS
    Changes the admin password for a CompleteView Recording Server via API.

.DESCRIPTION
    This script connects to a Recording Server API, retrieves the current configuration,
    updates the admin password, and verifies the change. It includes comprehensive
    input validation, progress feedback, and error handling.

.NOTES
    Author:         <EMAIL>
    Creation Date:  01/19/24
    Version 1.1 (01/20/24) - Added a request for the RS IP so that the script isn't bound to run on the local server.
    Version 1.2 (01/20/25) - Enhanced user experience with better validation, progress feedback, and error handling.

#>

# Enhanced input validation functions
function Test-IPAddress {
    param([string]$ipAddress)

    if ([string]::IsNullOrWhiteSpace($ipAddress)) {
        return $false
    }

    # Try parsing as IP address first
    try {
        $ip = [System.Net.IPAddress]::Parse($ipAddress)
        # Ensure it's IPv4 and not loopback/multicast
        return $ip.AddressFamily -eq 'InterNetwork' -and
               -not [System.Net.IPAddress]::IsLoopback($ip) -and
               -not $ip.ToString().StartsWith('224.') -and
               -not $ip.ToString().StartsWith('239.')
    }
    catch {
        return $false
    }
}

function Test-NetworkConnectivity {
    param(
        [string]$ServerIP,
        [int]$Port = 4502,
        [int]$TimeoutMs = 5000
    )

    Write-Host "Testing network connectivity to $ServerIP`:$Port..." -ForegroundColor Yellow

    try {
        $tcpClient = New-Object System.Net.Sockets.TcpClient
        $asyncResult = $tcpClient.BeginConnect($ServerIP, $Port, $null, $null)
        $waitHandle = $asyncResult.AsyncWaitHandle

        if ($waitHandle.WaitOne($TimeoutMs)) {
            $tcpClient.EndConnect($asyncResult)
            $tcpClient.Close()
            Write-Host "✓ Connection successful" -ForegroundColor Green
            return $true
        }
        else {
            $tcpClient.Close()
            Write-Host "✗ Connection timeout" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "✗ Connection failed: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Show-ProgressStep {
    param(
        [string]$Message,
        [string]$Color = "Cyan"
    )

    Write-Host ""
    Write-Host "[$((Get-Date).ToString('HH:mm:ss'))] $Message" -ForegroundColor $Color
}



# Enhanced IP Address input with connectivity testing
Write-Host ""
Write-Host "========== Recording Server Password Change Utility ==========" -ForegroundColor Cyan
Write-Host "This script will change the admin password for a Recording Server" -ForegroundColor White
Write-Host ""

Show-ProgressStep "Getting Recording Server Information"

do {
    $serverIp = Read-Host -Prompt "Enter Recording Server IP address"

    if (-not (Test-IPAddress -ipAddress $serverIp)) {
        Write-Host "✗ Invalid IP address format. Please enter a valid IPv4 address (e.g., *************)" -ForegroundColor Red
        continue
    }

    Write-Host "✓ Valid IP format: $serverIp" -ForegroundColor Green

    # Test connectivity
    if (-not (Test-NetworkConnectivity -ServerIP $serverIp)) {
        Write-Host "Warning: Cannot connect to $serverIp`:4502" -ForegroundColor Yellow
        $continue = Read-Host "Continue anyway? (y/N)"
        if ($continue -notmatch '^[Yy]') {
            continue
        }
    }

    break
} while ($true)

# Construct base URL with the provided IP
$apiUrl = "http://${serverIp}:4502/v2.0/config"

Show-ProgressStep "Getting Current Credentials"

do {
    $rsUser = Read-Host "Enter current Recording Server username"
    if ([string]::IsNullOrWhiteSpace($rsUser)) {
        Write-Host "✗ Username cannot be blank. Please enter a valid username." -ForegroundColor Red
    }
    else {
        Write-Host "✓ Username: $rsUser" -ForegroundColor Green
    }
} while ([string]::IsNullOrWhiteSpace($rsUser))

# Prompt for Recording Server password
$rsPass = Read-Host "Enter current Recording Server password" -AsSecureString
Write-Host "✓ Current credentials obtained" -ForegroundColor Green


# Convert SecureString to plain text
$marshal = [System.Runtime.InteropServices.Marshal]
$plainPassword = $marshal::PtrToStringAuto($marshal::SecureStringToBSTR($rsPass))

# Base64 encode the initial creds
$base64AuthInfo = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes(("{0}:{1}" -f $rsUser, $plainPassword)))

# Setting up headers for GET request
$headers = @{
    Authorization = "Basic $base64AuthInfo"
}

# Getting RS config with enhanced feedback
Show-ProgressStep "Retrieving Recording Server Configuration"

try {
    Write-Host "Connecting to API endpoint: $apiUrl" -ForegroundColor Gray
    $jsonContent = Invoke-RestMethod -Uri $apiUrl -Method Get -Headers $headers -TimeoutSec 30
    Write-Host "✓ Configuration retrieved successfully" -ForegroundColor Green

    # Validate that we have the expected structure
    if (-not $jsonContent.'server-config' -or -not $jsonContent.'server-config'.users) {
        throw "Invalid configuration structure received from Recording Server"
    }

    $userCount = $jsonContent.'server-config'.users.Count
    Write-Host "✓ Found $userCount user(s) in configuration" -ForegroundColor Green

} catch {
    Write-Host "✗ Failed to retrieve Recording Server configuration" -ForegroundColor Red
    Write-Host "Error details: $($_.Exception.Message)" -ForegroundColor Red

    if ($_.Exception.Message -match "401|Unauthorized") {
        Write-Host "This appears to be an authentication error. Please verify your credentials." -ForegroundColor Yellow
    }
    elseif ($_.Exception.Message -match "timeout") {
        Write-Host "Connection timed out. Please verify the Recording Server is running and accessible." -ForegroundColor Yellow
    }

    Read-Host "Press Enter to exit"
    exit 1
}

# Enhanced password input
Show-ProgressStep "Setting New Password"

$passwordMatched = $false
do {
    Write-Host ""
    $newPassword = Read-Host "Enter the new Recording Server password (leave blank for empty password)" -AsSecureString
    $confirmPassword = Read-Host "Confirm the new Recording Server password" -AsSecureString

    $newPasswordPlainText = [System.Runtime.InteropServices.Marshal]::PtrToStringAuto([System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($newPassword))
    $confirmPasswordPlainText = [System.Runtime.InteropServices.Marshal]::PtrToStringAuto([System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($confirmPassword))

    if ($newPasswordPlainText -ne $confirmPasswordPlainText) {
        Write-Host "✗ Passwords do not match. Please try again." -ForegroundColor Red
        continue
    }

    # Show what type of password was set
    if ([string]::IsNullOrEmpty($newPasswordPlainText)) {
        Write-Host "✓ Empty password confirmed" -ForegroundColor Green
    } else {
        Write-Host "✓ Password confirmed" -ForegroundColor Green
    }
    $passwordMatched = $true

} while (-not $passwordMatched)

# Updating password for the specified user
Show-ProgressStep "Updating Configuration"

$userFound = $false
$jsonContent.'server-config'.users | ForEach-Object {
    if ($_.name -eq $rsUser) {
        $_.password = $newPasswordPlainText
        $userFound = $true
        Write-Host "✓ Password updated for user: $($_.name)" -ForegroundColor Green
    }
}

if (-not $userFound) {
    Write-Host "✗ User '$rsUser' not found in configuration" -ForegroundColor Red
    Write-Host "Available users:" -ForegroundColor Yellow
    $jsonContent.'server-config'.users | ForEach-Object {
        Write-Host "  • $($_.name)" -ForegroundColor Yellow
    }
    Read-Host "Press Enter to exit"
    exit 1
}

# Converting back to JSON and updating configuration
Show-ProgressStep "Applying Configuration Changes"

try {
    Write-Host "Converting configuration to JSON..." -ForegroundColor Gray
    $jsonString = $jsonContent | ConvertTo-Json -Depth 100

    Write-Host "Uploading updated configuration..." -ForegroundColor Gray
    Invoke-RestMethod -Uri $apiUrl -Method Put -Body $jsonString -Headers $headers -ContentType "application/json" -TimeoutSec 30
    Write-Host "✓ Configuration successfully updated on Recording Server" -ForegroundColor Green

} catch {
    Write-Host "✗ Failed to update Recording Server configuration" -ForegroundColor Red
    Write-Host "Error details: $($_.Exception.Message)" -ForegroundColor Red

    if ($_.Exception.Message -match "401|Unauthorized") {
        Write-Host "Authentication failed during update. The original credentials may have changed." -ForegroundColor Yellow
    }

    Read-Host "Press Enter to exit"
    exit 1
}

# Enhanced verification step
Show-ProgressStep "Verifying Password Change"

try {
    Write-Host "Testing authentication with new password..." -ForegroundColor Gray

    # Using new password for auth
    $newBase64AuthInfo = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes(("{0}:{1}" -f $rsUser, $newPasswordPlainText)))

    # Set up headers for verification GET
    $headersVerification = @{
        Authorization = "Basic $newBase64AuthInfo"
    }

    # Test authentication with new credentials
    $verificationResult = Invoke-RestMethod -Uri $apiUrl -Method Get -Headers $headersVerification -TimeoutSec 15

    Write-Host "✓ Verification successful: Password change completed successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "========== SUMMARY ==========" -ForegroundColor Cyan
    Write-Host "Recording Server: $serverIp" -ForegroundColor White
    Write-Host "User: $rsUser" -ForegroundColor White
    Write-Host "Status: Password successfully changed and verified" -ForegroundColor Green
    Write-Host ""

} catch {
    Write-Host "✗ Verification failed: Unable to authenticate with new password" -ForegroundColor Red
    Write-Host "Error details: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "⚠ WARNING: The password may have been changed but verification failed." -ForegroundColor Yellow
    Write-Host "Please manually verify the new password works before proceeding." -ForegroundColor Yellow
    Write-Host ""
}

# Clean up sensitive data from memory
$newPasswordPlainText = $null
$confirmPasswordPlainText = $null
$plainPassword = $null
[System.GC]::Collect()

Write-Host "Press Enter to exit..." -ForegroundColor Gray
Read-Host
